# Copyright (c) 2025 PaddlePaddle Authors. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""FastAPI应用入口文件。

本文件创建FastAPI应用实例并注册可视化工具模块的路由。
"""

from fastapi import FastAPI, Response
from util.proc_stats import ProcStats
from util.logging_util import setup_logging, get_logger
from util.middleware import AccessLogMiddleware, TraceMiddleware, PrometheusMiddleware
from prometheus_client import Counter, Histogram, generate_latest
from fastapi import APIRouter
from pydantic import BaseModel, Field
from typing import Optional, Any, Dict
from util.trace_util import get_trace_logger, log_request_response
from image_tool.processor import apply_image_tool, SUPPORTED_IMAGE_TOOLS
from video_tool.processor import apply_video_tool, SUPPORTED_VIDEO_TOOLS
from util.bos_util import get_bos_client, bucket
from PIL import Image
from io import BytesIO
import base64, requests, os
from urllib.parse import urlsplit
import time as _time
# ------------------- Prometheus metrics -------------------
REQUEST_COUNT = Counter(
    "visual_tools_requests_total", "Total requests", ["method", "endpoint"]
)
REQUEST_LATENCY = Histogram(
    "visual_tools_request_latency_seconds", "Request latency", ["endpoint"]
)
# 初始化日志系统
setup_logging()
logger = get_logger("visual_tool")

# 创建FastAPI应用实例
app = FastAPI(title="Visual Tools API", description="图像处理工具API", version="1.0.0")

# 添加中间件（注意顺序：TraceMiddleware应该在最前面）
app.add_middleware(TraceMiddleware)
app.add_middleware(AccessLogMiddleware)
app.add_middleware(
    PrometheusMiddleware, request_count=REQUEST_COUNT, request_latency=REQUEST_LATENCY
)

# 在此文件中定义单个路由，并实现内部路由逻辑
router = APIRouter()


class V1Request(BaseModel):
    name: Optional[str] = ""
    version: Optional[str] = ""
    call_id: Optional[str] = ""
    arguments: Dict[str, Any] = Field(default_factory=dict)
    extra_params: Dict[str, Any] = Field(default_factory=dict)


def make_resp(code: int, message: str, type: str = "", data: Any | None = None):
    dataReturn = data
    if code == 0 and type != "":
        dict_data = {"type": type, type: data}
        try:
            import json

            dataReturn = json.dumps(dict_data)
        except TypeError:
            dataReturn = data
    return {"code": code, "message": message, "data": dataReturn}


@router.post("/visual_tools/v1/visual_tool")
async def api_v1(payload: V1Request):
    request_data = {
        "name": payload.name,
        "version": payload.version,
        "call_id": payload.call_id,
        "arguments": payload.arguments,
        "extra_params": payload.extra_params,
    }
    logger_local = get_trace_logger("visual_tool")
    try:
        name = payload.name or ""
        if not name:
            error_code = 2001
            error_resp = make_resp(error_code, "Tool name connot be empty")
            log_request_response(logger_local, request_data, error_resp)
            return error_resp

        # 解析参数
        args = dict(payload.arguments or {})
        extra_params = dict(payload.extra_params or {})

        # 基于名称做内部路由
        if name in SUPPORTED_IMAGE_TOOLS:
            try:
                resp_type, resp_data = apply_image_tool(name, args, extra_params)
                success_resp = make_resp(0, "Finish", resp_type, resp_data)
                log_request_response(logger_local, request_data, success_resp)
                return success_resp
            except ValueError as ve:
                error_code = 2005
                error_resp = make_resp(error_code, f"Tool execution failed: {ve}")
                log_request_response(logger_local, request_data, error_resp, ve)
                return error_resp
            except Exception as e:
                error_code = 2003
                error_resp = make_resp(error_code, f"Tool internal error: internal error: {e}")
                log_request_response(logger_local, request_data, error_resp, e)
                return error_resp

        elif name in SUPPORTED_VIDEO_TOOLS:
            try:
                resp_type, resp_data = apply_video_tool(name, args, extra_params)
                success_resp = make_resp(0, "Finish", resp_type, resp_data)
                log_request_response(logger_local, request_data, success_resp)
                return success_resp
            except ValueError as ve:
                error_code = 2005
                error_resp = make_resp(error_code, f"Tool execution failed: {ve}")
                log_request_response(logger_local, request_data, error_resp, ve)
                return error_resp
            except Exception as e:
                error_code = 2003
                error_resp = make_resp(error_code, f"Tool internal error: {e}")
                log_request_response(logger_local, request_data, error_resp, e)
                return error_resp
        else:
            error_code = 2001
            error_resp = make_resp(error_code, f"Tool not found: {name}")
            log_request_response(logger_local, request_data, error_resp)
            return error_resp
    except Exception as e:
        error_code = 4003
        error_resp = make_resp(error_code, f"Tool internal error: {e}")
        log_request_response(logger_local, request_data, error_resp, e)
        return error_resp


# 将本文件的单个路由注册到应用
app.include_router(router)


@app.get("/health")
async def health():
    """Return the health status of the tool server."""
    return {"status": "ok", "proc_stats": ProcStats.stats()}


@app.get("/metrics")
async def metrics():
    """返回 Prometheus 指标数据"""
    return Response(generate_latest(), media_type="text/plain")


if __name__ == "__main__":
    import uvicorn

    uvicorn.run("app:app", host="0.0.0.0", port=8003, reload=True)
