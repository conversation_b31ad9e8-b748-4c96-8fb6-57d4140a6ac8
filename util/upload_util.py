import os
import time
import hashlib
import random
from typing import Optional

from util.bos_util import get_bos_client, bucket
from util.trace_util import get_trace_logger


def upload_file_to_bos(file_path: str, object_key_prefix: str = "visual_tools/output") -> str:
    """上传文件到BOS并返回访问URL。

    - 计算MD5
    - 生成时间戳与随机数
    - 组装 object_key
    - 执行上传并生成可访问URL
    - 全程记录日志
    """
    logger = get_trace_logger("visual_tool")

    if not os.path.exists(file_path):
        logger.error("upload_file_to_bos: file_not_found", extra={
            "file_path": file_path,
        })
        raise FileNotFoundError(file_path)

    file_size = os.path.getsize(file_path)
    size_kb = round(file_size / 1024.0, 2)
    _, ext = os.path.splitext(file_path)

    logger.info("upload_start", extra={
        "file_path": file_path,
        "size_kb": size_kb,
        "prefix": object_key_prefix,
    })

    # 计算MD5
    file_md5 = hashlib.md5(open(file_path, "rb").read()).hexdigest()
    ts_ms = int(time.time() * 1000)
    rand5 = random.randint(10000, 99999)

    object_key = f"{object_key_prefix}/{file_md5}_{ts_ms}_{rand5}{ext}"

    try:
        get_bos_client().put_object_from_file(bucket, object_key, file_path)
        bos_url = get_bos_client().generate_pre_signed_url(
            bucket, object_key, expiration_in_seconds=-1
        )
        url_str = bos_url.decode("utf-8") if isinstance(bos_url, (bytes, bytearray)) else str(bos_url)
        logger.info("upload_success", extra={
            "file_path": file_path,
            "size_kb": size_kb,
            "object_key": object_key,
            "url": url_str,
        })
        return url_str
    except Exception as e:
        logger.error("upload_failed", extra={
            "file_path": file_path,
            "size_kb": size_kb,
            "object_key": object_key,
            "error": str(e),
        })
        raise

