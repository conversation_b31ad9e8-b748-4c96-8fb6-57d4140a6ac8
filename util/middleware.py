# Copyright (c) 2025 PaddlePaddle Authors. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""中间件模块。

本模块提供FastAPI应用的中间件功能，包括访问日志记录等。
"""

import time
import logging
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from util.logging_util import get_access_logger
from util.trace_util import generate_trace_id, set_trace_id, get_trace_logger



def _should_skip_logging(path: str) -> bool:
    """判断是否为应跳过日志打印的健康检查/监控路径。"""
    return path in ("/metrics", "/health")


class AccessLogMiddleware(BaseHTTPMiddleware):
    """访问日志中间件。

    记录所有HTTP请求的访问日志，包括请求方法、路径、状态码、响应时间等信息。
    """

    def __init__(self, app):
        super().__init__(app)
        self.access_logger = get_access_logger()

    async def dispatch(self, request: Request, call_next):
        """处理HTTP请求并记录访问日志。

        参数:
            request (Request): HTTP请求对象
            call_next: 下一个中间件或路由处理器

        返回:
            Response: HTTP响应对象
        """
        start_time = time.time()

        # 获取客户端IP
        client_ip = request.client.host if request.client else "unknown"

        # 获取用户代理
        user_agent = request.headers.get("user-agent", "unknown")

        # 处理请求
        response = await call_next(request)

        # 计算处理时间
        process_time = time.time() - start_time

        # 格式化处理时间
        if process_time < 1:
            time_str = f"{process_time * 1000:.0f}ms"
        else:
            time_str = f"{process_time:.1f}s"

        # 记录访问日志（跳过 /metrics 与 /health）
        if not _should_skip_logging(request.url.path):
            log_message = (
                f"{client_ip} - \"{request.method} {request.url.path}\" "
                f"{response.status_code} - {time_str} - \"{user_agent}\""
            )
            self.access_logger.info(log_message)

        return response


class TraceMiddleware(BaseHTTPMiddleware):
    """TraceID中间件。

    为每个请求生成或提取TraceID，并设置到上下文中。
    """

    def __init__(self, app):
        super().__init__(app)
        self.logger = get_trace_logger()

    async def dispatch(self, request: Request, call_next):
        """处理HTTP请求并设置TraceID。

        参数:
            request (Request): HTTP请求对象
            call_next: 下一个中间件或路由处理器

        返回:
            Response: HTTP响应对象
        """
        # 记录开始时间
        start_time = time.time()

        # 从请求头中获取TraceID，如果没有则生成新的
        trace_id = request.headers.get("X-Trace-ID") or generate_trace_id()
        set_trace_id(trace_id)

        # 记录请求开始（跳过 /metrics 与 /health）
        if not _should_skip_logging(request.url.path):
            self.logger.info(f"请求开始: {request.method} {request.url.path}")

        try:
            # 处理请求
            response = await call_next(request)

            # 计算处理时间
            process_time = time.time() - start_time

            # 在响应头中添加TraceID
            response.headers["X-Trace-ID"] = trace_id

            # 格式化处理时间
            if process_time < 1:
                time_str = f"{process_time * 1000:.0f}ms"
            else:
                time_str = f"{process_time:.1f}s"

            # 记录请求完成（包含耗时，跳过 /metrics 与 /health）
            if not _should_skip_logging(request.url.path):
                self.logger.info(f"请求完成: {response.status_code} - 耗时: {time_str}")

            return response

        except Exception as e:
            # 计算处理时间（即使出错也要记录）
            process_time = time.time() - start_time
            if process_time < 1:
                time_str = f"{process_time * 1000:.0f}ms"
            else:
                time_str = f"{process_time:.1f}s"

            # 记录请求异常（包含耗时，跳过 /metrics 与 /health）
            if not _should_skip_logging(request.url.path):
                self.logger.error(f"请求异常: {str(e)} - 耗时: {time_str}")
            raise


class PrometheusMiddleware(BaseHTTPMiddleware):
    """Prometheus监控中间件。

    收集和记录请求指标数据，用于Prometheus监控。
    """

    def __init__(self, app, request_count, request_latency):
        super().__init__(app)
        self.request_count = request_count
        self.request_latency = request_latency

    async def dispatch(self, request: Request, call_next):
        """处理HTTP请求并收集监控指标。

        参数:
            request (Request): HTTP请求对象
            call_next: 下一个中间件或路由处理器

        返回:
            Response: HTTP响应对象
        """
        method = request.method
        endpoint = request.url.path

        # 增加请求计数
        self.request_count.labels(method=method, endpoint=endpoint).inc()

        # 记录请求延迟
        with self.request_latency.labels(endpoint=endpoint).time():
            response = await call_next(request)

        return response
